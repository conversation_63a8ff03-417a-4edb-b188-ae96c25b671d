ПОЛНОЕ ТЕХНИЧЕСКОЕ ОПИСАНИЕ TELEGRAM БОТА ДЛЯ ПЕРЕПИСЫВАНИЯ НА AIOGRAM
================================================================================

ОБЩАЯ АРХИТЕКТУРА БОТА
======================

Это сложный многофункциональный Telegram бот для генерации подкастов и работы с ИИ, построенный на pyTeleBot с использованием множественных API сервисов. Бот поддерживает как приватные чаты, так и групповые, с развитой системой администрирования и контроля доступа.

ОСНОВНЫЕ КОМПОНЕНТЫ:
- Основной движок: pyTeleBot (telebot)
- База данных: SQLite3 (chat_messages.db)
- Многопоточность: ThreadPoolExecutor с глобальным менеджером пулов
- Система логирования: Ротация файлов с уровнями логирования
- Система мониторинга: Отслеживание производительности и ресурсов
- Система кэширования: Глобальные словари с thread-safe доступом

СТРУКТУРА ФАЙЛОВ:
- main.py - точка входа и инициализация
- config.py - все конфигурационные константы
- bot_globals.py - глобальные переменные и состояния
- handlers.py - обработчики команд и сообщений (7913 строк)
- processing_core.py - основная логика обработки (5123 строки)
- api_clients.py - клиенты для всех API (1851 строка)
- database.py - работа с базой данных (1193 строки)
- admin_system.py - система администрирования (1048 строк)
- utils.py - утилиты и вспомогательные функции (3715 строк)

API КЛИЕНТЫ И НАСТРОЙКИ
=======================

1. GEMINI API (Основной ИИ)
---------------------------
Модели:
- gemini-2.5-pro (основная модель для сложных задач)
- gemini-2.5-flash (быстрая модель для простых задач)
- gemini-2.5-flash-lite (облегченная версия)

Настройки API:
- URL: https://generativelanguage.googleapis.com/v1beta/models/{model}:generateContent
- Ключи: OFFICIAL_GEMINI_API_KEYS (список из нескольких ключей)
- Ротация ключей: автоматическая при ошибках
- Таймауты: GENAI_REQUEST_TIMEOUT (по умолчанию 300 сек)
- Retry логика: до 5 попыток с экспоненциальной задержкой

Функции:
- call_official_gemini_api_genai() - основной вызов
- call_gemini_2_5_flash_api_genai() - быстрая модель
- call_official_gemini_api_genai_stream() - стриминг
- call_gemini_2_5_flash_api_genai_stream() - стриминг быстрой модели

2. GEMINI TTS API (Генерация речи)
----------------------------------
Модель: gemini-2.5-flash-preview-tts
Настройки:
- Поддержка многоголосого TTS для подкастов
- Голоса подкастов: Diana (женский), Alexander (мужской)
- Альтернативные голоса: Anna, Mikhail
- Таймаут: GENAI_TTS_TIMEOUT (по умолчанию 600 сек)
- Fallback на VoidAI TTS при ошибках

Функции:
- call_gemini_tts_api_genai() - основная генерация TTS
- call_gemini_single_tts_api_genai() - одиночный голос

3. VOIDAI API (Резервный ИИ)
----------------------------
Настройки:
- URL: VOIDAI_API_HOST
- Ключи: VOIDAI_API_KEYS (список ключей)
- Модели: поддержка различных моделей через параметр model_name

Функции:
- call_voidai_api_sync() - синхронный вызов
- Поддержка изображений через inline_data
- Обработка истории разговоров

4. NAVY API (Grok-4 и генерация изображений)
--------------------------------------------
Настройки:
- URL: https://api.navy/v1/chat/completions
- Ключ: NAVY_API_KEY = "sk-55a958382b1eb58ae5729b5b164f0babd4c8014ec89ed32a"
- Модель: grok-4 (для подкастов)
- Изображения: NAVY_IMAGE_API_KEY для генерации изображений

Функции:
- call_navy_api_sync() - основной вызов Grok-4
- call_navy_image_generate_api_sync() - генерация изображений

5. CEREBRAS API (Классификация сложности)
-----------------------------------------
Настройки:
- URL: https://api.cerebras.ai/v1/chat/completions
- Ключи: CEREBRAS_API_KEYS (4 ключа с ротацией)
- Модели: 
  - llama-4-maverick-17b-128e-instruct (классификация)
  - qwen-3-235b-a22b (суммаризация)

Функции:
- classify_complexity() - определение сложности запроса
- summarize_text() - сокращение длинных текстов

КОМАНДЫ БОТА
============

ОСНОВНЫЕ КОМАНДЫ:
/start - приветствие и информация о боте
/help - справка по командам
/s [текст] - быстрый ответ с реакцией
/ultrathink - переключение в режим глубокого мышления

КОМАНДЫ ПОДКАСТОВ:
/podcast - генерация подкаста за последние 24 часа
/podcast [часы] - подкаст за указанное количество часов (макс 72)
/podcast [тема] - тематический подкаст за 24 часа
/podcast [часы] [тема] - тематический подкаст за указанное время

АДМИНИСТРАТИВНЫЕ КОМАНДЫ:
/adminf - панель администратора со всеми функциями
/addadmin [telegram_id] - добавить администратора
/removeadmin [telegram_id] - удалить администратора
/block [user_id/username] - заблокировать пользователя
/unblock [user_id/username] - разблокировать пользователя
/unlock - разблокировать бота в группе
/stats - статистика использования бота

КОМАНДЫ УПРАВЛЕНИЯ ПОДКАСТАМИ:
/podcastevery [время] - настроить ежедневный подкаст (например: /podcastevery 20:00)
/podcaststop - отключить ежедневный подкаст
/podcastschedule - показать расписание подкастов
/blockpodcast - заблокировать подкасты для обычных пользователей в группе
/unblockpodcast - разблокировать подкасты для всех в группе

КОМАНДЫ МОДЕЛИ:
/gemini - принудительно переключить на Gemini (только админы)
/grok on/off - переключить генерацию подкастов между Gemini и Grok-4

СИСТЕМЫ УПРАВЛЕНИЯ ДОСТУПОМ
============================

1. СИСТЕМА АДМИНИСТРАТОРОВ
--------------------------
Файл: admin_system.py
Хранение: bot_data.json -> admin_data.admins[]
Функции:
- is_admin(user_id) - проверка прав администратора
- add_admin(user_id, added_by) - добавление админа
- remove_admin(user_id, removed_by) - удаление админа
- get_admin_list() - список всех админов

2. СИСТЕМА БЛОКИРОВКИ ПОЛЬЗОВАТЕЛЕЙ
-----------------------------------
Хранение: bot_data.json -> blocked_users[]
Функции:
- is_user_blocked(user_id) - проверка блокировки
- block_user(user_id, admin_id) - блокировка пользователя
- unblock_user(user_id, admin_id) - разблокировка
- block_user_by_username(username, admin_id) - блокировка по username

3. СИСТЕМА PRO-ПОЛЬЗОВАТЕЛЕЙ
-----------------------------
Хранение: bot_data.json -> pro_users{}
Функции:
- is_pro_user(user_id) - проверка PRO статуса
- activate_pro_user(user_id) - активация PRO
- deactivate_pro_user(user_id) - деактивация PRO
- get_pro_multiplier(user_id) - получение множителя лимитов

4. СИСТЕМА КОНТРОЛЯ ДОСТУПА К ГРУППАМ
-------------------------------------
Файл: access_control.py
База данных: unlocked_groups таблица
Функции:
- check_message_access(message) - проверка доступа к сообщению
- is_group_unlocked(chat_id) - проверка разблокировки группы
- unlock_group(chat_id, admin_id) - разблокировка группы

СИСТЕМА ОГРАНИЧЕНИЙ (RATE LIMITING)
===================================

Файл: rate_limiter.py
Класс: RateLimiter

ТИПЫ ОГРАНИЧЕНИЙ:

1. Сообщения (анти-спам):
- Лимит: 3 сообщения в 2 секунды
- Функция: check_message_rate(user_id)

2. Подкасты в группах:
- Дневной лимит: PODCAST_DAILY_LIMIT (по умолчанию 2)
- Кулдаун: PODCAST_COOLDOWN (6 часов между подкастами)
- Функция: check_podcast_limit(user_id)

3. Тематические подкасты:
- Лимит: 1 в день на пользователя
- Функция: check_theme_podcast_limit(user_id)

4. Приватные чаты:
- Подкасты: PRIVATE_PODCAST_DAILY_LIMIT
- ИИ ответы: PRIVATE_AI_RESPONSE_DAILY_LIMIT  
- Суммаризация: PRIVATE_SUMMARY_DAILY_LIMIT
- Функция: check_private_limit(user_id, limit_type, daily_limit)

5. Генерация видео:
- Лимит: 1 видео в 5 минут для обычных пользователей
- PRO пользователи: без ограничений
- Функция: check_video_generation_limit(user_id)

ОБРАБОТКА РАЗЛИЧНЫХ ТИПОВ КОНТЕНТА
===================================

1. ТЕКСТОВЫЕ СООБЩЕНИЯ
----------------------
Обработчик: handle_text_message() в handlers.py
Логика:
- Проверка доступа через access_control
- Определение сложности через Cerebras API
- Выбор модели: LITE -> Flash, HARD -> Pro, ULTRAPRO -> Pro
- Поддержка стриминга (если DISABLE_STREAMING = False)
- Автоматическая суммаризация длинных ответов

2. ИЗОБРАЖЕНИЯ
--------------
Обработчик: handle_photo_message() в handlers.py
Поддержка:
- Одиночные изображения
- Медиа-группы (альбомы)
- Обработка через media_groups словарь с таймером
- Конвертация в base64 для API
- Поддержка подписей к изображениям

3. АУДИО И ВИДЕО
----------------
Обработчики: handle_audio_message(), handle_video_message()
Функции:
- Транскрипция через Gemini API
- Конвертация в MP3 через FFmpeg
- Группировка аудио/видео файлов
- Суммаризация длинных записей
- Поддержка голосовых сообщений

4. ДОКУМЕНТЫ
------------
Обработчик: handle_document_message()
Поддерживаемые форматы:
- PDF (если PDF_SUPPORTED = True)
- Текстовые файлы: .txt, .md, .py, .js, .html, .css, .json, .xml
- Извлечение текста и обработка через ИИ

5. ПЕРЕСЛАННЫЕ СООБЩЕНИЯ
------------------------
Обработчик: handle_forwarded_message()
Особенности:
- Группировка пересланных сообщений в батчи
- Обработка через forwarded_audio_queue
- Поддержка аудио из пересланных сообщений

ГЕНЕРАЦИЯ ПОДКАСТОВ
===================

Основная функция: generate_podcast() в processing_core.py

ЭТАПЫ ГЕНЕРАЦИИ:

1. СБОР СООБЩЕНИЙ
-----------------
- Извлечение сообщений из базы данных за указанный период
- Фильтрация по типу контента (текст, медиа)
- Группировка по пользователям и времени

2. ПОДГОТОВКА КОНТЕКСТА
-----------------------
- Формирование истории разговоров
- Добавление метаданных (время, пользователи)
- Обработка специальных символов и форматирования

3. ГЕНЕРАЦИЯ ДИАЛОГА
--------------------
Системные промпты:
- SYSTEM_PROMPT_PODCAST_DIANA_ALEXANDER - основной промпт для подкастов
- Персонажи: Diana (ведущая), Alexander (эксперт)
- Поддержка тематических подкастов через дополнительные промпты

Модели:
- По умолчанию: Gemini 2.5 Pro
- Альтернатива: Grok-4 (через команду /grok on)

4. ГЕНЕРАЦИЯ АУДИО
------------------
- Использование Gemini TTS API
- Многоголосый режим (Diana + Alexander)
- Fallback на VoidAI TTS при ошибках
- Сохранение в формате MP3

5. ОТПРАВКА РЕЗУЛЬТАТА
----------------------
- Отправка аудио файла в чат
- Добавление описания и метаданных
- Логирование результатов

ТЕМАТИЧЕСКИЕ ПОДКАСТЫ:
- Поддержка исследовательских запросов
- Функции: generate_research_queries(), perform_multiple_searches()
- Интеграция результатов поиска в контекст подкаста

БАЗА ДАННЫХ
===========

Файл: database.py
База: SQLite3 (chat_messages.db)

ТАБЛИЦЫ:

1. chat_messages - основные сообщения
-----------------------------------
Поля:
- id (PRIMARY KEY)
- chat_id, user_id, username, first_name, last_name
- message_text, message_id, timestamp
- message_type, is_forwarded, forward_from_username
- reply_to_message_id, reply_to_user_id, reply_to_username
- message_date, message_time, time_period

2. podcast_timestamps - отслеживание подкастов
----------------------------------------------
Поля:
- chat_id (PRIMARY KEY)
- last_podcast_time, updated_at
- last_podcast_message_id

3. unlocked_groups - разблокированные группы
--------------------------------------------
Поля:
- chat_id (PRIMARY KEY)
- unlocked_by, unlocked_at
- group_title, group_username

4. blocked_podcasts_groups - блокировка подкастов
-------------------------------------------------
Поля:
- chat_id (PRIMARY KEY)
- blocked_by, blocked_at

5. api_requests - логирование API запросов
------------------------------------------
Поля:
- id, timestamp, api_name, model_name
- user_id, chat_id, request_type
- tokens_used, response_time, success

ФУНКЦИИ БАЗЫ ДАННЫХ:
- save_message() - сохранение сообщения
- get_chat_messages() - получение сообщений чата
- log_api_request() - логирование API запросов
- cleanup_old_messages() - очистка старых сообщений
- get_api_usage_stats() - статистика использования API

СИСТЕМЫ МОНИТОРИНГА И ЛОГИРОВАНИЯ
==================================

1. СИСТЕМА ЛОГИРОВАНИЯ
----------------------
Файл: bot_globals.py
Функция: log_admin()

Настройки:
- Файл логов: bot.log
- Ротация: RotatingFileHandler (10MB, 5 файлов)
- Уровни: DEBUG, INFO, WARNING, ERROR
- Формат: [TIMESTAMP] [LEVEL] MESSAGE

2. МОНИТОРИНГ ПРОИЗВОДИТЕЛЬНОСТИ
--------------------------------
Файл: performance_monitor.py (если существует)
Отслеживание:
- Время выполнения API запросов
- Использование памяти
- Количество активных потоков
- Статистика ошибок

3. МОНИТОРИНГ РЕСУРСОВ
----------------------
Файл: monitoring_commands.py
Функции:
- check_bot_processes() - проверка процессов бота
- check_threads() - мониторинг потоков
- check_memory() - использование памяти
- monitor_continuously() - непрерывный мониторинг

УТИЛИТЫ И ВСПОМОГАТЕЛЬНЫЕ МОДУЛИ
=================================

1. УПРАВЛЕНИЕ ПОТОКАМИ
----------------------
Файл: thread_pool_manager.py
Класс: ThreadPoolManager
- Глобальный пул потоков (16 воркеров)
- Автоматическая очистка завершенных задач
- Статистика выполнения
- Функции: submit_task(), submit_task_with_timeout()

2. RETRY ЛОГИКА
---------------
Файл: retry_utils.py
Декораторы:
- @retry_on_censorship - повтор при ошибках цензуры
- @retry_on_censorship_async - асинхронная версия
- Функция: is_censorship_error() - определение ошибок цензуры

3. ОБРАБОТКА MARKDOWN И HTML
----------------------------
Файл: utils.py
Функции:
- fix_telegram_markdown_v2() - исправление Markdown V2
- convert_markdown_to_html() - конвертация в HTML
- escape_html() - экранирование HTML
- clean_response_text() - очистка текста ответов

4. РАБОТА С АУДИО/ВИДЕО
-----------------------
Функции:
- convert_to_mp3() - конвертация в MP3 через FFmpeg
- get_audio_duration() - получение длительности
- run_ffmpeg_async() - асинхронное выполнение FFmpeg

5. TELEGRAPH ИНТЕГРАЦИЯ
-----------------------
Файл: telegraph_helpers.py
Функции:
- setup_telegraph() - настройка Telegraph клиента
- create_telegraph_page() - создание страницы
- Поддержка длинных текстов через Telegraph

СПЕЦИАЛЬНЫЕ МОДУЛИ
==================

1. HTML ГЕНЕРАЦИЯ
-----------------
Файл: html_group.py
Функции:
- generate_html_site() - генерация HTML сайтов
- Использование Gemini 2.5 Pro и Cerebras API
- Поддержка изображений в запросах
- Интеграция ИИ в генерируемые сайты

2. АВТОМАТИЧЕСКАЯ СУММАРИЗАЦИЯ
------------------------------
Файл: summ.py
Функция: check_and_summarize_text()
- Автоматическое сокращение текстов >600 символов в группах
- Использование Cerebras API (qwen-3-235b-a22b)
- Показ степени сжатия текста

КОНФИГУРАЦИЯ И НАСТРОЙКИ
========================

Файл: config.py

ОСНОВНЫЕ КОНСТАНТЫ:
- BOT_TOKEN - токен Telegram бота
- ADMIN_CHAT_ID - ID чата администратора для логов

API КЛЮЧИ:
- OFFICIAL_GEMINI_API_KEYS - список ключей Gemini
- VOIDAI_API_KEYS - ключи VoidAI
- NAVY_API_KEY - ключ Navy API
- NAVY_IMAGE_API_KEY - ключ для генерации изображений

ЛИМИТЫ И ТАЙМАУТЫ:
- PODCAST_DAILY_LIMIT = 2 - дневной лимит подкастов
- PODCAST_COOLDOWN = 21600 - кулдаун между подкастами (6 часов)
- MAX_AUDIO_DURATION = 6000 - максимальная длительность аудио
- GENAI_REQUEST_TIMEOUT = 300 - таймаут API запросов
- GENAI_TTS_TIMEOUT = 600 - таймаут TTS генерации

СИСТЕМНЫЕ ПРОМПТЫ:
- SYSTEM_PROMPT_MAIN - основной промпт для чатов
- SYSTEM_PROMPT_GROUP - промпт для групп
- SYSTEM_PROMPT_PODCAST_DIANA_ALEXANDER - промпт для подкастов
- SYSTEM_PROMPT_TRANSCRIBE - промпт для транскрипции
- SYSTEM_PROMPT_SUMMARIZE - промпт для суммаризации

ГОЛОСА TTS:
- PODCAST_WOMAN_VOICE = "Diana" - женский голос
- PODCAST_MAN_VOICE = "Alexander" - мужской голос
- PODCAST_ANNA_VOICE = "Anna" - альтернативный женский
- PODCAST_MIKHAIL_VOICE = "Mikhail" - альтернативный мужской

НАСТРОЙКИ ОБРАБОТКИ:
- MEDIA_GROUP_DELAY = 1.0 - задержка обработки медиа-групп
- AUDIO_VIDEO_GROUP_DELAY = 0.1 - задержка аудио/видео групп
- FORWARD_BATCH_DELAY = 0.1 - задержка пересланных сообщений
- SUMMARIZE_THRESHOLD = 500 - порог для суммаризации
- TELEGRAPH_THRESHOLD = 8885 - порог для Telegraph
- TELEGRAM_MSG_LIMIT = 4000 - лимит длины сообщения

ПОДДЕРЖИВАЕМЫЕ ФОРМАТЫ:
- SUPPORTED_DOC_EXTENSIONS - расширения документов
- PDF_SUPPORTED - поддержка PDF
- TELEGRAPH_SUPPORTED - поддержка Telegraph

РЕКОМЕНДАЦИИ ПО ПЕРЕПИСЫВАНИЮ НА AIOGRAM
=========================================

1. АРХИТЕКТУРНЫЕ ИЗМЕНЕНИЯ:
---------------------------
- Заменить pyTeleBot на aiogram 3.x
- Использовать aiogram.Router для организации хендлеров
- Применить aiogram.FSM для управления состояниями
- Использовать aiogram.middlewares для общей логики

2. ОБРАБОТЧИКИ СООБЩЕНИЙ:
-------------------------
- Конвертировать @bot.message_handler в @router.message()
- Заменить @bot.callback_query_handler на @router.callback_query()
- Использовать aiogram.filters для фильтрации сообщений
- Применить aiogram.types для типизации

3. API КЛИЕНТЫ:
---------------
- Сохранить существующую логику API клиентов
- Адаптировать под async/await паттерн aiogram
- Использовать aiohttp вместо requests для HTTP запросов
- Сохранить систему ротации ключей и retry логику

4. БАЗА ДАННЫХ:
---------------
- Рассмотреть переход на asyncpg/aiosqlite для асинхронности
- Сохранить существующую структуру таблиц
- Адаптировать функции под async/await
- Добавить connection pooling для производительности

5. СИСТЕМЫ УПРАВЛЕНИЯ:
----------------------
- Сохранить логику admin_system.py
- Адаптировать rate_limiter под aiogram middleware
- Интегрировать access_control как middleware
- Сохранить систему логирования с адаптацией под aiogram

6. КОНФИГУРАЦИЯ:
----------------
- Использовать pydantic для валидации конфигурации
- Сохранить все существующие настройки и константы
- Добавить переменные окружения для чувствительных данных
- Использовать aiogram.Bot и aiogram.Dispatcher

7. МНОГОПОТОЧНОСТЬ:
-------------------
- Заменить ThreadPoolExecutor на asyncio.create_task()
- Использовать asyncio.Queue вместо queue.Queue
- Адаптировать thread_pool_manager под asyncio
- Сохранить логику обработки медиа-групп с таймерами

8. СПЕЦИАЛЬНЫЕ МОДУЛИ:
----------------------
- Адаптировать html_group.py под async
- Сохранить логику summ.py с async обработкой
- Интегрировать Telegraph через async клиент
- Сохранить FFmpeg обработку через subprocess

КЛЮЧЕВЫЕ ОСОБЕННОСТИ ДЛЯ СОХРАНЕНИЯ:
- Система ротации API ключей
- Логика генерации подкастов с TTS
- Система ограничений и контроля доступа
- Обработка различных типов медиа
- Автоматическая суммаризация и классификация сложности
- Система администрирования и мониторинга
- Поддержка как приватных чатов, так и групп
- Интеграция множественных AI сервисов

Этот документ содержит полное описание всех аспектов бота для успешного переписывания на aiogram с сохранением всей функциональности.
